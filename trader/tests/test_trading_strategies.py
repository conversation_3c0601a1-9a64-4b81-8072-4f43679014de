"""
Testes function-based para estratégias de trading.
Validam comportamento de compra e venda com mínimo de mocking.
"""

from datetime import datetime
from decimal import Decimal

from trader.account import Position, Sides
from trader.trading_strategy import (
    PercentageStrategy,
    SimpleMovingAverageStrategy,
)

order_counter = 1


def create_position(
    quantity: Decimal = Decimal("0.1"),
    entry_price: Decimal = Decimal("100.0"),
    current_price: Decimal | None = None,
) -> Position:
    """Helper para criar posições de teste"""
    global order_counter
    order_counter += 1
    return Position(
        order_id=f"test-order-{order_counter}",
        symbol="BTC-BRL",
        side=Sides.LONG,
        quantity=quantity,
        entry_price=entry_price,
        entry_time=datetime.now(),
        current_price=current_price or entry_price,
    )


def test_simple_moving_average_strategy_buy_signal():
    """Testa se SMA strategy compra quando SMA curta cruza acima da longa"""
    strategy = SimpleMovingAverageStrategy(short_period=3, long_period=5)

    # Preços que criam tendência de baixa seguida de alta
    prices = [
        Decimal("100"),
        Decimal("95"),
        Decimal("90"),
        Decimal("85"),
        Decimal("80"),  # SMA longa baixa
        Decimal("85"),
        Decimal("90"),
        Decimal("95"),  # SMA curta sobe
    ]

    position = create_position()

    # Alimenta histórico de preços
    for price in prices:
        strategy.on_market_refresh(price, position, [])

    # Deve comprar quando SMA curta > SMA longa
    assert strategy.should_buy(Decimal("95")) is True

    # SMA curta (3): (85+90+95)/3 = 90
    # SMA longa (5): (80+85+90+95)/4 = 87.5 (só tem 4 valores dos últimos 5)
    # Como 90 > 87.5, deve comprar


def test_simple_moving_average_strategy_sell_signal():
    """Testa se SMA strategy vende quando SMA curta cruza abaixo da longa"""
    strategy = SimpleMovingAverageStrategy(short_period=3, long_period=5)

    # Preços que criam tendência de alta seguida de baixa
    prices = [
        Decimal("80"),
        Decimal("85"),
        Decimal("90"),
        Decimal("95"),
        Decimal("100"),  # SMA longa alta
        Decimal("95"),
        Decimal("90"),
        Decimal("85"),  # SMA curta desce
    ]

    position = create_position()

    # Alimenta histórico de preços
    for price in prices:
        strategy.on_market_refresh(price, position, [])

    # Deve vender quando SMA curta < SMA longa
    assert strategy.should_sell(Decimal("85"), position) is True


def test_simple_moving_average_strategy_insufficient_data():
    """Testa se SMA strategy não opera sem dados suficientes"""
    strategy = SimpleMovingAverageStrategy(short_period=10, long_period=30)
    position = create_position()

    # Apenas alguns preços (insuficientes para long_period)
    for price in [Decimal("100"), Decimal("101"), Decimal("102")]:
        strategy.on_market_refresh(price, position, [])

    # Não deve comprar nem vender sem dados suficientes
    assert strategy.should_buy(Decimal("103")) is False
    assert strategy.should_sell(Decimal("103"), position) is False


def test_calculate_quantity_methods():
    """Testa se os métodos de cálculo de quantidade funcionam corretamente"""
    balance = Decimal("1000")
    price = Decimal("100")

    # SMA strategy usa 10% do saldo
    sma_strategy = SimpleMovingAverageStrategy()
    sma_quantity = sma_strategy.calculate_quantity(balance, price)
    expected_sma = (balance * Decimal("0.1")) / price  # 1.0
    assert Decimal(sma_quantity) == expected_sma


def test_percentage_strategy_buy_signal():
    """Testa se PercentageStrategy compra quando preço cai X%"""
    strategy = PercentageStrategy(
        buy_drop_percentage=2.0,
        stop_loss_percentage=5.0,
        position_percentage=0.8
    )

    # Primeiro preço estabelece referência
    strategy.on_market_refresh(Decimal("100"), None, [])
    assert strategy.reference_price == Decimal("100")

    # Preço cai 1% - não deve comprar ainda
    signal = strategy.on_market_refresh(Decimal("99"), None, [])
    assert signal is None

    # Preço cai 2% - deve comprar
    signal = strategy.on_market_refresh(Decimal("98"), None, [])
    assert signal is not None
    assert signal.side == "buy"

    # Referência deve ser atualizada para o preço de compra
    assert strategy.reference_price == Decimal("98")


def test_percentage_strategy_sell_signal():
    """Testa se PercentageStrategy vende no stop loss dinâmico"""
    strategy = PercentageStrategy(
        buy_drop_percentage=2.0,
        stop_loss_percentage=5.0,
        position_percentage=0.8
    )

    # Simula posição existente
    position = create_position(
        quantity=Decimal("0.1"),
        entry_price=Decimal("100"),
        current_price=Decimal("100")
    )

    # Preço sobe para 110 (novo máximo)
    strategy.on_market_refresh(Decimal("110"), position, [])
    assert strategy.position_max_price == Decimal("110")

    # Stop loss = 110 * 0.95 = 104.5
    # Preço cai para 105 - não deve vender ainda
    signal = strategy.on_market_refresh(Decimal("105"), position, [])
    assert signal is None

    # Preço cai para 104 - deve vender (abaixo do stop loss)
    signal = strategy.on_market_refresh(Decimal("104"), position, [])
    assert signal is not None
    assert signal.side == "sell"
    assert signal.quantity == position.quantity


def test_percentage_strategy_reference_price_update():
    """Testa se o preço de referência é atualizado corretamente"""
    strategy = PercentageStrategy(buy_drop_percentage=2.0)

    # Estabelece referência inicial
    strategy.on_market_refresh(Decimal("100"), None, [])
    assert strategy.reference_price == Decimal("100")

    # Preço sobe - deve atualizar referência
    strategy.on_market_refresh(Decimal("105"), None, [])
    assert strategy.reference_price == Decimal("105")

    # Preço cai um pouco - não deve atualizar referência
    strategy.on_market_refresh(Decimal("103"), None, [])
    assert strategy.reference_price == Decimal("105")


def test_percentage_strategy_max_price_tracking():
    """Testa se o preço máximo da posição é rastreado corretamente"""
    strategy = PercentageStrategy(stop_loss_percentage=5.0)
    position = create_position(entry_price=Decimal("100"))

    # Primeiro preço da posição
    strategy.on_market_refresh(Decimal("100"), position, [])
    assert strategy.position_max_price == Decimal("100")

    # Preço sobe
    strategy.on_market_refresh(Decimal("110"), position, [])
    assert strategy.position_max_price == Decimal("110")

    # Preço cai - máximo não deve mudar
    strategy.on_market_refresh(Decimal("105"), position, [])
    assert strategy.position_max_price == Decimal("110")

    # Preço sobe mais - máximo deve ser atualizado
    strategy.on_market_refresh(Decimal("115"), position, [])
    assert strategy.position_max_price == Decimal("115")


def test_percentage_strategy_no_position_resets_max_price():
    """Testa se o preço máximo é resetado quando não há posição"""
    strategy = PercentageStrategy()
    position = create_position()

    # Estabelece preço máximo com posição
    strategy.on_market_refresh(Decimal("110"), position, [])
    assert strategy.position_max_price == Decimal("110")

    # Remove posição - preço máximo deve ser resetado
    strategy.on_market_refresh(Decimal("100"), None, [])
    assert strategy.position_max_price is None
