# Estratégia Baseada em Percentual (PercentageStrategy)

## Descrição

A `PercentageStrategy` é uma estratégia de trading automatizada que opera baseada em percentuais de variação de preço. Esta estratégia implementa uma abordagem conservadora de compra em quedas e venda com stop loss dinâmico.

## Como Funciona

### Lógica de Compra
- **Trigger**: Compra quando o preço cai X% em relação ao preço de referência
- **Preço de Referência**: Inicialmente é o primeiro preço observado, depois é atualizado para:
  - O preço atual após uma compra
  - O preço mais alto observado (se maior que a referência atual)

### Lógica de Venda (Stop Loss Dinâmico)
- **Trigger**: Vende quando o preço atinge o percentual de stop loss
- **Cálculo do Stop Loss**: Baseado no valor **máximo** atingido pela posição
- **Fórmula**: `stop_loss_price = max_price * (100 - stop_loss_percentage) / 100`

### Exemplo Prático

Suponha uma configuração com:
- `buy_drop_percentage = 2.0%`
- `stop_loss_percentage = 5.0%`

**Cenário:**
1. Preço inicial: R$ 100.000
2. Preço cai para R$ 98.000 (-2%) → **COMPRA**
3. Preço sobe para R$ 105.000 (novo máximo)
4. Stop loss calculado: R$ 105.000 * 0.95 = R$ 99.750
5. Preço cai para R$ 99.500 → **VENDA** (stop loss atingido)

## Parâmetros Configuráveis

| Parâmetro | Tipo | Padrão | Descrição |
|-----------|------|--------|-----------|
| `buy_drop_percentage` | float | 2.0 | Percentual de queda para comprar (%) |
| `stop_loss_percentage` | float | 5.0 | Percentual de stop loss do máximo (%) |
| `position_percentage` | float | 0.8 | Percentual do saldo a usar (0.0-1.0) |

## Vantagens

✅ **Stop Loss Dinâmico**: Protege lucros ao ajustar o stop loss conforme o preço sobe
✅ **Compra em Quedas**: Aproveita correções de mercado para entrar em posições
✅ **Gestão de Risco**: Controla o tamanho da posição e perdas máximas
✅ **Simples e Eficaz**: Lógica clara e fácil de entender

## Limitações

⚠️ **Mercados Laterais**: Pode gerar muitas operações em mercados sem tendência
⚠️ **Quedas Bruscas**: Em crashes severos, pode comprar em "facas caindo"
⚠️ **Sem Take Profit**: Não tem mecanismo automático de realização de lucros

## Como Usar

### 1. Execução Básica (Simulação)

```bash
python main.py --strategy=percentage --currency=BTC-BRL --interval=5 --fake
```

### 2. Com Parâmetros Personalizados

```bash
python main.py --strategy=percentage --currency=BTC-BRL --interval=5 --fake \
  --buy_drop_percentage=1.5 \
  --stop_loss_percentage=3.0 \
  --position_percentage=0.5
```

### 3. Salvando Dados em CSV

```bash
python main.py --strategy=percentage --currency=BTC-BRL --interval=5 --fake \
  --persistence=file
```

### 4. Usando o Exemplo Pronto

```bash
# Execução com configurações padrão
python example_percentage_strategy.py

# Execução com parâmetros personalizados
python example_percentage_strategy.py --buy_drop_percentage=1.0 --stop_loss_percentage=4.0
```

## Configurações Recomendadas

### Conservadora (Baixo Risco)
```bash
--buy_drop_percentage=3.0 --stop_loss_percentage=3.0 --position_percentage=0.3
```

### Moderada (Risco Médio)
```bash
--buy_drop_percentage=2.0 --stop_loss_percentage=5.0 --position_percentage=0.5
```

### Agressiva (Alto Risco)
```bash
--buy_drop_percentage=1.0 --stop_loss_percentage=7.0 --position_percentage=0.8
```

## Monitoramento

A estratégia mantém internamente:
- **Histórico de Preços**: Últimos 100 preços para análise
- **Preço de Referência**: Para cálculo de quedas
- **Preço Máximo da Posição**: Para cálculo do stop loss dinâmico

## Logs e Debugging

O bot gera logs coloridos mostrando:
- 🟢 Sinais de compra
- 🔴 Sinais de venda
- 💰 Lucros realizados
- 💸 Prejuízos realizados

## Backtesting

Para testar a estratégia com dados históricos, use a API fake que simula condições reais de mercado.

## Próximos Passos

Possíveis melhorias futuras:
- [ ] Implementar take profit automático
- [ ] Adicionar filtros de volume
- [ ] Implementar trailing stop mais sofisticado
- [ ] Adicionar indicadores técnicos como filtros
